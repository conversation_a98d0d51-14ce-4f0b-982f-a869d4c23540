# Circle Deployment Package

## 🎯 Overview

Circle is a Linear-inspired project management interface that:
- **No Database Required**: Uses browser local storage (Zustand)
- **No Environment Variables**: Works out of the box
- **Mock Data**: Comes with pre-populated example data
- **Modern UI**: Built with Next.js 15 and shadcn/ui

## 📦 Deployment Options

### Option 1: Direct Server Deployment (Recommended)

**Requirements:**
- Ubuntu/Debian server
- 2GB RAM minimum
- Node.js 20.x
- Nginx (for reverse proxy)

**Quick Deploy:**
1. Copy `deploy-circle.sh` to your server
2. Run: `chmod +x deploy-circle.sh && ./deploy-circle.sh`
3. Access at: http://your-server-ip

### Option 2: Docker Deployment

**Requirements:**
- Docker & Docker Compose installed
- 2GB RAM minimum

**Commands:**
```bash
# Clone repository
git clone https://github.com/ln-dev7/circle.git
cd circle

# Build and run
docker-compose up -d

# Access at http://your-server-ip:3000
```

### Option 3: Proxmox LXC Container

**Best for Proxmox users:**
1. Create Ubuntu 22.04 LXC container
2. Allocate 2GB RAM, 10GB disk
3. Use the deployment script inside container

## 🚀 Quick Start Commands

```bash
# For server deployment
wget https://raw.githubusercontent.com/ln-dev7/circle/main/deploy-circle.sh
chmod +x deploy-circle.sh
./deploy-circle.sh

# For Docker
docker run -d -p 3000:3000 --name circle circle-app

# For development
pnpm install
pnpm dev
```

## 📁 Files Included

1. **deploy-circle.sh** - Automated deployment script
2. **DEPLOY_CIRCLE_MANUAL.md** - Step-by-step manual guide
3. **Dockerfile** - Docker containerization
4. **docker-compose.yml** - Docker Compose configuration

## 🔧 Post-Deployment

### Accessing Circle
- Default URL: http://your-server-ip
- Default port: 3000 (behind Nginx on port 80)

### Managing the Application
```bash
# With PM2
pm2 status          # Check status
pm2 logs circle     # View logs
pm2 restart circle  # Restart app

# With Docker
docker ps                    # Check status
docker logs circle-app       # View logs
docker restart circle-app    # Restart app
```

### Customization
Since Circle uses mock data, customize by editing:
- `/mock-data/` - All mock data files
- `/store/` - Zustand state management
- `/components/` - UI components

## 🛡️ Security Considerations

1. **Firewall**: Only expose ports 80/443
2. **SSL**: Use Let's Encrypt for HTTPS
3. **Authentication**: Circle has no built-in auth - add Nginx basic auth if needed
4. **Data**: All data stored in browser - no server persistence

## 📊 Resource Requirements

- **Minimum**: 1 CPU, 1GB RAM, 5GB disk
- **Recommended**: 2 CPU, 2GB RAM, 10GB disk
- **Network**: Low bandwidth usage (static assets only)

## 🆘 Troubleshooting

### Common Issues

1. **Port already in use**:
   ```bash
   lsof -i :3000
   kill -9 <PID>
   ```

2. **Build failures**:
   ```bash
   rm -rf node_modules .next
   pnpm install
   pnpm build
   ```

3. **Nginx 502 errors**:
   - Check PM2: `pm2 status`
   - Check logs: `pm2 logs circle`

## 📚 Architecture Notes

Circle is a **client-side application**:
- Next.js serves static pages
- All logic runs in the browser
- Zustand manages state locally
- No API calls or backend needed
- Perfect for teams wanting self-hosted simplicity

## 🎨 Features

- **Issue Management**: Create, update, delete issues
- **Project Organization**: Group issues by project
- **Team Management**: Assign issues to team members
- **Labels & Priorities**: Categorize and prioritize work
- **Drag & Drop**: Reorder issues easily
- **Search & Filter**: Find issues quickly
- **Dark Mode**: Built-in theme support

---

**Ready to deploy?** Choose your preferred method above and have Circle running in minutes!