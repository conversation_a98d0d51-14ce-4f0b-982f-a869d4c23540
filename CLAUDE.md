# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ExpoBase is a React Native starter template using Expo SDK 53 with file-based routing, NativeWind styling, Zustand state management, and Supabase backend integration.

## Essential Commands

```bash
# Development
npm start              # Start Expo development server
npm run ios           # Run on iOS simulator
npm run android       # Run on Android emulator
npm run web          # Run web version

# Code Quality
npm run lint         # Run ESLint and <PERSON><PERSON><PERSON> checks
npm run format       # Auto-fix linting and formatting issues

# Setup & Configuration
npm run init         # Initial project setup wizard
npm run setup:env    # Configure environment variables
npm run supabase:setup # Setup Supabase database and functions

# Building
npm run prebuild     # Generate native iOS/Android projects
```

## Architecture Overview

### Core Stack
- **Routing**: Expo Router (file-based) with authentication guards
- **State**: Zustand stores with MMKV persistence for offline support
- **Styling**: NativeWind (Tailwind CSS for React Native)
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Edge Functions)
- **Payments**: RevenueCat + Stripe integration

### Key Directories
- `/app` - File-based routes organized by authentication state
  - `(auth)` - Public authentication screens
  - `(protected)` - Authenticated user screens  
  - `(tabs)` - Tab navigation structure
- `/components` - Feature-based component organization
- `/hooks` - Business logic hooks (auth, networking, etc.)
- `/stores` - Zustand state stores
- `/supabase` - Database migrations and edge functions
- `/types` - TypeScript definitions

### State Management Pattern
The app uses Zustand with MMKV persistence for offline-first functionality. Stores are located in `/stores` and follow this pattern:
- Persist critical data with MMKV
- Use React Query for server state
- Implement optimistic updates for better UX

### Authentication Flow
Multi-provider authentication via Supabase:
1. Providers: Email, Google, Apple
2. Protected routes use `_layout.tsx` guards
3. Session management in `useAuth` hook
4. Deep linking for OAuth callbacks

### Testing Approach
Currently no test suite implemented. When adding tests:
- Use Jest with React Native Testing Library
- Test critical business logic in hooks
- Focus on integration tests for authentication flows

## Development Guidelines

### Environment Setup
1. Copy `.env.example` to `.env.local`
2. Run `npm run setup:env` to configure Supabase
3. Update bundle identifier in `app.json` for your project

### Common Development Tasks

**Adding a new screen:**
- Create file in `/app` following existing patterns
- Protected routes go under `(protected)` or `(tabs)`
- Use `<Screen>` component for consistent styling

**Working with Supabase Edge Functions:**
- Functions are in `/supabase/functions`
- Test locally with `supabase functions serve`
- Environment variables in `.env.local`

**Implementing Premium Features:**
- Use `usePremiumStatus` hook for subscription state
- Wrap features with `<PremiumFeatureGate>`
- Configure products in RevenueCat dashboard

### Code Quality Standards
- TypeScript strict mode enabled
- ESLint + Prettier for formatting
- Path aliases configured (@components, @hooks, etc.)
- Always run `npm run lint` before committing

## Important Security Notes
- Never commit `.env.local` or expose API keys
- The current `app.json` contains test keys that should be replaced
- Use environment variables for all sensitive configuration
- Implement proper input validation for user-generated content