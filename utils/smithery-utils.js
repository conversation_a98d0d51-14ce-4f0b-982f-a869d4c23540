const { Client } = require('@smithery/sdk');
const SmitheryIntegration = require('../scripts/smithery-integration');

class SmitheryUtils {
  constructor() {
    this.integration = new SmitheryIntegration();
    this.client = null;
  }

  async initializeClient(apiKey = null) {
    if (apiKey) {
      this.client = new Client({ apiKey });
    }
    return this.client;
  }

  async autoDetectMCPNeeds(userMessage) {
    const patterns = {
      youtube: /youtube|video|transcript/i,
      webScraping: /scrape|crawl|fetch.*web|get.*data.*from.*site/i,
      database: /database|sql|query|store.*data/i,
      fileOperations: /read.*file|write.*file|file.*operations/i,
      memory: /remember|store.*information|memory/i,
      search: /search|find|look.*up/i,
      api: /api|rest|endpoint|http.*request/i,
      image: /image|photo|picture|visual/i,
      audio: /audio|sound|music|voice/i
    };

    const needs = [];
    for (const [category, pattern] of Object.entries(patterns)) {
      if (pattern.test(userMessage)) {
        needs.push(category);
      }
    }

    return needs;
  }

  async suggestMCPServers(needs) {
    const serverMap = {
      youtube: ['@sinco-lab/mcp-youtube-transcript', '@jikime/py-mcp-youtube-toolbox'],
      webScraping: ['@modelcontextprotocol/server-puppeteer', 'firecrawl-mcp'],
      database: ['@modelcontextprotocol/server-sqlite', '@upstash/context7-mcp'],
      fileOperations: ['@modelcontextprotocol/server-filesystem'],
      memory: ['@modelcontextprotocol/server-memory'],
      search: ['exa', '@upstash/context7-mcp'],
      api: ['@modelcontextprotocol/server-fetch'],
      image: ['@modelcontextprotocol/server-puppeteer'],
      audio: ['@sinco-lab/mcp-youtube-transcript']
    };

    const suggestions = new Set();
    needs.forEach(need => {
      if (serverMap[need]) {
        serverMap[need].forEach(server => suggestions.add(server));
      }
    });

    return Array.from(suggestions);
  }

  async checkMissingServers(suggestedServers) {
    const config = this.integration.loadConfig();
    const installed = Object.keys(config.mcpServers || {});
    
    const missing = suggestedServers.filter(server => {
      const normalizedServer = server.replace('@', '').replace('/', '-');
      return !installed.includes(normalizedServer) && 
             !installed.includes(server) &&
             !installed.some(inst => inst.includes(server.split('/')[1] || server));
    });

    return missing;
  }

  async autoInstallForTask(userMessage) {
    const needs = await this.autoDetectMCPNeeds(userMessage);
    
    if (needs.length === 0) {
      return { installed: [], message: 'No specific MCP requirements detected' };
    }

    const suggested = await this.suggestMCPServers(needs);
    const missing = await this.checkMissingServers(suggested);

    if (missing.length === 0) {
      return { installed: [], message: 'All required MCP servers already available' };
    }

    console.log(`🤖 Auto-installing ${missing.length} MCP servers for detected needs: ${needs.join(', ')}`);
    
    const installed = [];
    for (const server of missing) {
      try {
        const success = await this.integration.installServer(server);
        if (success) {
          installed.push(server);
        }
      } catch (error) {
        console.error(`Failed to install ${server}:`, error.message);
      }
    }

    return { 
      installed, 
      needs,
      message: `Installed ${installed.length}/${missing.length} recommended servers`
    };
  }

  async getRecommendationsForContext(taskType, complexity = 'basic') {
    const complexityMap = {
      basic: 1,
      intermediate: 2, 
      advanced: 3
    };

    const recommendations = {
      'data-analysis': {
        1: ['@modelcontextprotocol/server-memory', '@upstash/context7-mcp'],
        2: ['@modelcontextprotocol/server-sqlite', 'exa'],
        3: ['@modelcontextprotocol/server-puppeteer', '@modelcontextprotocol/server-fetch']
      },
      'web-development': {
        1: ['@modelcontextprotocol/server-filesystem'],
        2: ['@modelcontextprotocol/server-puppeteer', '@modelcontextprotocol/server-fetch'],
        3: ['firecrawl-mcp', '@upstash/context7-mcp']
      },
      'content-creation': {
        1: ['@sinco-lab/mcp-youtube-transcript'],
        2: ['@jikime/py-mcp-youtube-toolbox', '@modelcontextprotocol/server-memory'],
        3: ['@modelcontextprotocol/server-puppeteer', 'exa']
      }
    };

    const level = complexityMap[complexity] || 1;
    const servers = recommendations[taskType] || {};
    
    // Get servers for current level and below
    const recommended = [];
    for (let i = 1; i <= level; i++) {
      if (servers[i]) {
        recommended.push(...servers[i]);
      }
    }

    return [...new Set(recommended)];
  }

  async quickInstall(serverName) {
    return await this.integration.installServer(serverName);
  }

  async listInstalled() {
    const config = this.integration.loadConfig();
    return Object.keys(config.mcpServers || {});
  }

  async searchRegistry(query, limit = 10) {
    return await this.integration.searchServers(query);
  }

  formatServerInfo(server) {
    return {
      name: server.qualifiedName,
      description: server.description,
      uses: server.useCount,
      remote: server.remote,
      installCommand: `npm run mcp-install ${server.qualifiedName}`
    };
  }
}

// Export singleton instance
const smitheryUtils = new SmitheryUtils();

module.exports = {
  SmitheryUtils,
  smitheryUtils,
  
  // Convenience functions
  autoInstallForTask: (message) => smitheryUtils.autoInstallForTask(message),
  suggestForTask: (message) => smitheryUtils.autoDetectMCPNeeds(message),
  quickInstall: (server) => smitheryUtils.quickInstall(server),
  searchServers: (query) => smitheryUtils.searchRegistry(query),
  listInstalled: () => smitheryUtils.listInstalled()
};