#!/bin/bash

# Circle Deployment Script for Ubuntu/Debian Server
# Run this script on your server at *************

set -e  # Exit on error

echo "🚀 Starting Circle deployment..."

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root"
   exit 1
fi

# Update system
print_status "Updating system packages..."
apt update && apt upgrade -y
print_success "System updated"

# Install Node.js 20.x
print_status "Installing Node.js 20.x..."
if ! command -v node &> /dev/null; then
    curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
    apt install -y nodejs
    print_success "Node.js installed: $(node --version)"
else
    print_status "Node.js already installed: $(node --version)"
fi

# Install pnpm
print_status "Installing pnpm..."
if ! command -v pnpm &> /dev/null; then
    npm install -g pnpm
    print_success "pnpm installed: $(pnpm --version)"
else
    print_status "pnpm already installed: $(pnpm --version)"
fi

# Install PM2
print_status "Installing PM2..."
if ! command -v pm2 &> /dev/null; then
    npm install -g pm2
    print_success "PM2 installed"
else
    print_status "PM2 already installed"
fi

# Install Nginx
print_status "Installing Nginx..."
if ! command -v nginx &> /dev/null; then
    apt install -y nginx
    print_success "Nginx installed"
else
    print_status "Nginx already installed"
fi

# Install Git
print_status "Installing Git..."
if ! command -v git &> /dev/null; then
    apt install -y git
    print_success "Git installed"
else
    print_status "Git already installed"
fi

# Create application directory
print_status "Setting up application directory..."
mkdir -p /var/www
cd /var/www

# Clone Circle repository
print_status "Cloning Circle repository..."
if [ -d "circle" ]; then
    print_status "Circle directory exists, pulling latest changes..."
    cd circle
    git pull
else
    git clone https://github.com/ln-dev7/circle.git
    cd circle
fi
print_success "Repository ready"

# Install dependencies
print_status "Installing dependencies with pnpm..."
pnpm install
print_success "Dependencies installed"

# Build the application
print_status "Building the application..."
pnpm build
print_success "Application built successfully"

# Create PM2 ecosystem file
print_status "Creating PM2 configuration..."
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'circle',
    script: 'node_modules/.bin/next',
    args: 'start',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    cwd: '/var/www/circle',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    error_file: '/var/log/circle/error.log',
    out_file: '/var/log/circle/out.log',
    log_file: '/var/log/circle/combined.log',
    time: true
  }]
}
EOF

# Create log directory
mkdir -p /var/log/circle

# Start application with PM2
print_status "Starting application with PM2..."
pm2 stop circle 2>/dev/null || true
pm2 start ecosystem.config.js
pm2 save
pm2 startup systemd -u root --hp /root

print_success "Application started with PM2"

# Configure Nginx
print_status "Configuring Nginx..."
cat > /etc/nginx/sites-available/circle << 'EOF'
server {
    listen 80;
    server_name _;

    # Redirect HTTP to app
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Increase client body size for file uploads
    client_max_body_size 10M;

    # Gzip compression
    gzip on;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml application/javascript application/json;
    gzip_vary on;
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/circle /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
nginx -t

# Restart Nginx
systemctl restart nginx
print_success "Nginx configured and restarted"

# Configure firewall (if ufw is installed)
if command -v ufw &> /dev/null; then
    print_status "Configuring firewall..."
    ufw allow 22/tcp
    ufw allow 80/tcp
    ufw allow 443/tcp
    ufw --force enable
    print_success "Firewall configured"
fi

# Show status
print_status "Checking application status..."
pm2 status

# Get server IP
SERVER_IP=$(hostname -I | awk '{print $1}')

echo ""
print_success "🎉 Circle deployment completed!"
echo ""
echo "Access your Circle instance at:"
echo "  http://$SERVER_IP"
echo "  http://*************"
echo ""
echo "Useful commands:"
echo "  pm2 status         - Check app status"
echo "  pm2 logs circle    - View app logs"
echo "  pm2 restart circle - Restart the app"
echo "  pm2 stop circle    - Stop the app"
echo ""
echo "Log files:"
echo "  /var/log/circle/error.log    - Error logs"
echo "  /var/log/circle/out.log      - Output logs"
echo "  /var/log/circle/combined.log - Combined logs"
echo ""