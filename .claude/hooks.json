{"hooks": {"pre_task": [{"command": "node", "args": ["scripts/smithery-integration.js", "check-dependencies"], "description": "Check if required MCP servers are available for the task"}], "slash_commands": {"/smithery": {"command": "node", "args": ["scripts/smithery-integration.js"], "description": "Interact with Smithery MCP registry", "usage": "/smithery <search|install|remove|list|recommended|details> [args...]"}, "/mcp-search": {"command": "node", "args": ["scripts/smithery-integration.js", "search"], "description": "Search for MCP servers on Smithery", "usage": "/mcp-search <query>"}, "/mcp-install": {"command": "node", "args": ["scripts/smithery-integration.js", "install"], "description": "Install an MCP server from Smithery", "usage": "/mcp-install <qualified-name> [server-name]"}, "/mcp-list": {"command": "node", "args": ["scripts/smithery-integration.js", "list"], "description": "List installed MCP servers", "usage": "/mcp-list"}}, "tool_suggestions": {"youtube": {"required_servers": ["@sinco-lab/mcp-youtube-transcript", "@jikime/py-mcp-youtube-toolbox"], "install_command": "node scripts/smithery-integration.js install @sinco-lab/mcp-youtube-transcript youtube-transcript"}, "web_scraping": {"required_servers": ["@modelcontextprotocol/server-puppeteer", "firecrawl-mcp"], "install_command": "node scripts/smithery-integration.js search 'web scraping'"}, "database": {"required_servers": ["@modelcontextprotocol/server-sqlite", "@upstash/context7-mcp"], "install_command": "node scripts/smithery-integration.js search 'database'"}, "memory": {"required_servers": ["@modelcontextprotocol/server-memory"], "install_command": "node scripts/smithery-integration.js install @modelcontextprotocol/server-memory"}}}}