{"permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "mcp__linear-mcp-server__list_teams", "mcp__linear-mcp-server__create_project", "mcp__linear-mcp-server__list_issue_statuses", "mcp__linear-mcp-server__create_issue", "WebFetch(domain:github.com)", "WebFetch(domain:raw.githubusercontent.com)", "Bash(ping:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(git clone:*)", "Bash(pnpm install:*)", "Bash(pnpm dev:*)", "<PERSON><PERSON>(curl:*)", "mcp__alias-workspace__search_emails", "mcp__alias-workspace__get_help", "<PERSON><PERSON>(python3:*)", "mcp__youtube-transcript__get_transcript", "Bash(npx:*)", "<PERSON><PERSON>(gcloud auth:*)", "Bash(gcloud projects list:*)", "Bash(gcloud alpha services api-keys list:*)", "Bash(gcloud services api-keys list:*)", "Bash(gcloud services api-keys get-key-string:*)", "<PERSON><PERSON>(pkill:*)", "Bash(rm:*)", "<PERSON><PERSON>(true)", "Bash(npm cache clean:*)", "mcp__desktop-commander__execute_command", "mcp__desktop-commander__read_file", "WebFetch(domain:smithery.ai)", "mcp__desktop-commander__edit_block", "WebFetch(domain:docs.smithery.ai)", "<PERSON><PERSON>(chmod:*)"], "deny": []}}