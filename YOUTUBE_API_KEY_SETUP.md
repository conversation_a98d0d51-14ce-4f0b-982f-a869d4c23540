# YouTube API Key Setup Guide

## 🎯 YouTube MCP Server Installed

I've added the **zubeid-youtube-mcp-server** to your configuration. This server uses the official YouTube Data API v3 and provides:

### Features:
- ✅ Video search and details
- ✅ Transcript extraction with timestamps
- ✅ Channel information
- ✅ Playlist management
- ✅ Multi-language support

## 🔑 Getting Your YouTube API Key

### Step 1: Access Google Cloud Console
1. Go to: https://console.cloud.google.com/
2. Sign in with your Google account

### Step 2: Create or Select Project
1. Click the project dropdown (top left)
2. Click "NEW PROJECT"
3. Name it: "YouTube MCP Server" (or similar)
4. Click "CREATE"

### Step 3: Enable YouTube Data API v3
1. In the search bar, type "YouTube Data API v3"
2. Click on "YouTube Data API v3"
3. Click "ENABLE"

### Step 4: Create API Credentials
1. Go to "APIs & Services" → "Credentials"
2. Click "+ CREATE CREDENTIALS" → "API key"
3. Your API key will be generated
4. Click "RESTRICT KEY" (important!)

### Step 5: Restrict Your API Key
1. Under "Application restrictions":
   - Choose "None" for now (can restrict later)
2. Under "API restrictions":
   - Select "Restrict key"
   - Choose "YouTube Data API v3"
3. Click "SAVE"

### Step 6: Copy Your API Key
Copy the API key from the credentials page.

## 📝 Update Your Configuration

Replace `YOUR_API_KEY_HERE` with your actual API key:

```bash
# Open the config file
open ~/Library/Application\ Support/Claude/claude_desktop_config.json
```

Find this section and add your key:
```json
"youtube-api": {
  "command": "npx",
  "args": ["-y", "zubeid-youtube-mcp-server"],
  "env": {
    "YOUTUBE_API_KEY": "AIza...[your-actual-key]",
    "YOUTUBE_TRANSCRIPT_LANG": "en"
  }
}
```

## 📊 API Quotas

YouTube Data API v3 has quotas:
- **Default**: 10,000 units per day
- **Search**: 100 units per request
- **Video details**: 1 unit per video
- **Transcripts**: 1 unit per request

Monitor usage at: https://console.cloud.google.com/apis/api/youtube.googleapis.com/metrics

## 🚀 Testing After Setup

1. **Restart Claude Desktop**
2. **Test commands**:
   - "Search YouTube for React tutorials"
   - "Get transcript from [YouTube URL]"
   - "Get video details for [Video ID]"

## 🔒 Security Tips

1. **Never share your API key publicly**
2. **Add IP restrictions** if using from specific locations
3. **Monitor usage** regularly
4. **Regenerate** if compromised

## 🆘 Troubleshooting

### "API key not valid"
- Check if API is enabled
- Verify key restrictions
- Wait 5 minutes after creation

### "Quota exceeded"
- Check daily usage in console
- Request quota increase if needed

### "403 Forbidden"
- API might not be enabled
- Key restrictions too strict

## 📚 Available Tools

Once configured, you'll have access to:
- `searchVideos` - Search YouTube videos
- `getVideo` - Get video details
- `getTranscript` - Extract video transcripts
- `getChannel` - Get channel information
- `getPlaylistItems` - List playlist videos

---

**Next Step**: Get your API key from Google Cloud Console and update the configuration!