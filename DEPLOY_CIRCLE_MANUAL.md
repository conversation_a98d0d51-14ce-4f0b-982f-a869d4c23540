# Circle Deployment Guide for Server *************

Since direct SSH access is not working, here are the manual steps to deploy Circle on your server.

## Option 1: Using the Deployment Script

1. **Transfer the script to your server**:
   ```bash
   # From your local machine
   scp deploy-circle.sh root@*************:/root/
   ```

2. **Access your server** (via Proxmox console or alternative SSH method)

3. **Run the deployment script**:
   ```bash
   chmod +x /root/deploy-circle.sh
   ./deploy-circle.sh
   ```

## Option 2: Manual Step-by-Step Deployment

### 1. Server Preparation

```bash
# Update system
apt update && apt upgrade -y

# Install Node.js 20.x
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
apt install -y nodejs

# Install pnpm
npm install -g pnpm

# Install PM2
npm install -g pm2

# Install Nginx
apt install -y nginx

# Install Git
apt install -y git
```

### 2. Clone and Build Circle

```bash
# Create directory
mkdir -p /var/www
cd /var/www

# Clone repository
git clone https://github.com/ln-dev7/circle.git
cd circle

# Install dependencies
pnpm install

# Build application
pnpm build
```

### 3. Configure PM2

Create `/var/www/circle/ecosystem.config.js`:

```javascript
module.exports = {
  apps: [{
    name: 'circle',
    script: 'node_modules/.bin/next',
    args: 'start',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    cwd: '/var/www/circle'
  }]
}
```

Start the application:
```bash
cd /var/www/circle
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 4. Configure Nginx

Create `/etc/nginx/sites-available/circle`:

```nginx
server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Enable the site:
```bash
ln -s /etc/nginx/sites-available/circle /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default
nginx -t
systemctl restart nginx
```

### 5. Configure Firewall (Optional)

```bash
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw enable
```

## Access Your Circle Instance

Once deployed, access Circle at:
- http://*************

## Important Notes

1. **No Database Required**: Circle uses mock data and browser storage
2. **No Environment Variables**: Circle doesn't require any .env configuration
3. **Data Persistence**: Data is stored in browser's local storage (Zustand)

## Customization Options

Since Circle uses mock data, you can customize it by editing files in:
- `/var/www/circle/mock-data/` - Contains all mock data
- `/var/www/circle/store/` - Zustand stores for state management

## Monitoring

```bash
# Check status
pm2 status

# View logs
pm2 logs circle

# Monitor resources
pm2 monit
```

## Troubleshooting

1. **Port 3000 already in use**:
   ```bash
   lsof -i :3000
   kill -9 <PID>
   ```

2. **Nginx errors**:
   ```bash
   nginx -t
   journalctl -u nginx
   ```

3. **PM2 issues**:
   ```bash
   pm2 delete all
   pm2 start ecosystem.config.js
   ```

## Alternative: Deploy in a Container

If you prefer containerized deployment on Proxmox:

1. Create an LXC container in Proxmox
2. Use Ubuntu 22.04 template
3. Allocate at least 2GB RAM and 10GB disk
4. Follow the deployment steps inside the container

## SSL Setup (Optional)

For HTTPS support with Let's Encrypt:
```bash
apt install certbot python3-certbot-nginx
certbot --nginx -d your-domain.com
```

---

Circle is now ready to use as your Linear-inspired project management tool!