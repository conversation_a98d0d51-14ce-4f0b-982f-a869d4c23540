# --- Supabase ---
# Your Supabase project URL (e.g., https://xxxxx.supabase.co)
EXPO_PUBLIC_SUPABASE_URL=
# Your Supabase anonymous key
EXPO_PUBLIC_SUPABASE_ANON_KEY=
# Your Supabase service role key (secret)
SUPABASE_SERVICE_ROLE_KEY=
# Your Supabase project reference (auto-extracted from URL)
SUPABASE_PROJECT_REF=

# --- Stripe (optional) ---
# Your Stripe publishable key (pk_test_... or pk_live_...)
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=
# Your Stripe secret key (sk_test_... or sk_live_...)
STRIPE_SECRET_KEY=
# Your Stripe webhook secret (whsec_...)
STRIPE_WEBHOOKS_SECRET=

# --- RevenueCat  ---
# Your RevenueCat API key
REVENUECAT_API_KEY=
# Your RevenueCat iOS key (appl_...)
EXPO_PUBLIC_RC_IOS_KEY=
# Your RevenueCat Android key (goog_...)
EXPO_PUBLIC_REVENUECAT_ANDROID_KEY=
# The subscription identifier that activates premium status in database (e.g., premium_monthly)
EXPO_PUBLIC_PREMIUM_SUBSCRIPTION_NAME=

# --- Sentry  ---
# Your Sentry DSN (https://...@sentry.io/...)
EXPO_PUBLIC_SENTRY_DSN=

SENTRY_DISABLE_AUTO_UPLOAD=true
# Your Sentry auth token
SENTRY_AUTH_TOKEN=

# --- Resend (optional) ---
# Your Resend API key (re_...)
RESEND_API_KEY=
# Your Resend from email address
RESEND_FROM_EMAIL=

# --- OpenAI (optional) ---
# Your OpenAI API key (sk-...)
OPENAI_API_KEY=

# --- Expo ---
# Your Expo access token for push notifiactions
EXPO_ACCESS_TOKEN=


# --- Google ----
# Provider connect with google
EXPO_PUBLIC_GOOGLE_CLIENT_ID=