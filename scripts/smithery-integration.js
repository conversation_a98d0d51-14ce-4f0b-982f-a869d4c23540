#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class SmitheryIntegration {
  constructor() {
    this.configPath = path.join(process.env.HOME, 'Library/Application Support/Claude/claude_desktop_config.json');
    this.backupPath = this.configPath + '.backup';
  }

  async searchServers(query) {
    try {
      console.log(`🔍 Searching Smithery for: ${query}`);
      
      // Use curl to search the registry
      const searchCmd = `curl -s "https://registry.smithery.ai/servers?q=${encodeURIComponent(query)}&pageSize=20"`;
      const result = execSync(searchCmd, { encoding: 'utf8' });
      const data = JSON.parse(result);
      
      return data.servers || [];
    } catch (error) {
      console.error('❌ Error searching Smithery:', error.message);
      return [];
    }
  }

  async getServerDetails(qualifiedName) {
    try {
      console.log(`📖 Getting details for: ${qualifiedName}`);
      
      const detailsCmd = `curl -s "https://registry.smithery.ai/servers/${qualifiedName}"`;
      const result = execSync(detailsCmd, { encoding: 'utf8' });
      return JSON.parse(result);
    } catch (error) {
      console.error('❌ Error getting server details:', error.message);
      return null;
    }
  }

  loadConfig() {
    try {
      if (fs.existsSync(this.configPath)) {
        return JSON.parse(fs.readFileSync(this.configPath, 'utf8'));
      }
      return { mcpServers: {} };
    } catch (error) {
      console.error('❌ Error loading config:', error.message);
      return { mcpServers: {} };
    }
  }

  saveConfig(config) {
    try {
      // Create backup
      if (fs.existsSync(this.configPath)) {
        fs.copyFileSync(this.configPath, this.backupPath);
      }
      
      // Ensure directory exists
      const dir = path.dirname(this.configPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      // Save config
      fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2));
      console.log('✅ Configuration saved successfully');
      console.log(`📋 Backup created at: ${this.backupPath}`);
    } catch (error) {
      console.error('❌ Error saving config:', error.message);
    }
  }

  async installServer(qualifiedName, serverName = null) {
    const details = await this.getServerDetails(qualifiedName);
    if (!details) {
      console.error('❌ Could not get server details');
      return false;
    }

    const config = this.loadConfig();
    const name = serverName || qualifiedName.replace('/', '-');

    // Find the best connection method
    const stdioConnection = details.connections?.find(c => c.type === 'stdio');
    const httpConnection = details.connections?.find(c => c.type === 'http');

    let serverConfig;

    if (stdioConnection) {
      // Use stdio connection with npx
      serverConfig = {
        command: "npx",
        args: ["-y", qualifiedName]
      };

      // Add environment variables if required by schema
      if (stdioConnection.configSchema?.properties) {
        serverConfig.env = {};
        console.log(`⚠️  Server ${qualifiedName} requires configuration:`);
        Object.keys(stdioConnection.configSchema.properties).forEach(key => {
          const prop = stdioConnection.configSchema.properties[key];
          console.log(`   ${key}: ${prop.description || prop.type}`);
        });
      }
    } else if (httpConnection) {
      // Use HTTP connection
      serverConfig = {
        command: "npx",
        args: ["-y", "mcp-remote", httpConnection.url],
        env: {}
      };
    } else {
      console.error('❌ No supported connection method found');
      return false;
    }

    config.mcpServers[name] = serverConfig;
    this.saveConfig(config);

    console.log(`✅ Installed ${qualifiedName} as '${name}'`);
    console.log('🔄 Restart Claude Desktop to use the new server');
    return true;
  }

  async removeServer(serverName) {
    const config = this.loadConfig();
    
    if (!config.mcpServers[serverName]) {
      console.error(`❌ Server '${serverName}' not found`);
      return false;
    }

    delete config.mcpServers[serverName];
    this.saveConfig(config);
    
    console.log(`✅ Removed server '${serverName}'`);
    console.log('🔄 Restart Claude Desktop to apply changes');
    return true;
  }

  listInstalledServers() {
    const config = this.loadConfig();
    const servers = Object.keys(config.mcpServers || {});
    
    if (servers.length === 0) {
      console.log('📭 No MCP servers installed');
      return;
    }

    console.log('📦 Installed MCP Servers:');
    servers.forEach(name => {
      const server = config.mcpServers[name];
      const command = server.args ? server.args.join(' ') : server.command;
      console.log(`   ${name}: ${command}`);
    });
  }

  async listRecommended() {
    const recommendations = [
      'youtube transcript',
      'file operations',
      'web scraping',
      'database',
      'api integration',
      'memory',
      'search'
    ];

    console.log('🌟 Recommended MCP servers by category:\n');

    for (const category of recommendations) {
      const servers = await this.searchServers(category);
      if (servers.length > 0) {
        console.log(`📂 ${category.toUpperCase()}:`);
        servers.slice(0, 3).forEach(server => {
          console.log(`   ${server.qualifiedName} - ${server.description.slice(0, 80)}...`);
        });
        console.log('');
      }
    }
  }
}

// CLI Interface
async function main() {
  const smithery = new SmitheryIntegration();
  const command = process.argv[2];
  const args = process.argv.slice(3);

  switch (command) {
    case 'search':
      if (!args[0]) {
        console.error('Usage: smithery search <query>');
        process.exit(1);
      }
      const servers = await smithery.searchServers(args[0]);
      console.log(`Found ${servers.length} servers:\n`);
      servers.forEach(server => {
        console.log(`📦 ${server.qualifiedName}`);
        console.log(`   ${server.description}`);
        console.log(`   Uses: ${server.useCount} | Remote: ${server.remote}`);
        console.log('');
      });
      break;

    case 'install':
      if (!args[0]) {
        console.error('Usage: smithery install <qualified-name> [server-name]');
        process.exit(1);
      }
      await smithery.installServer(args[0], args[1]);
      break;

    case 'remove':
      if (!args[0]) {
        console.error('Usage: smithery remove <server-name>');
        process.exit(1);
      }
      await smithery.removeServer(args[0]);
      break;

    case 'list':
      smithery.listInstalledServers();
      break;

    case 'recommended':
      await smithery.listRecommended();
      break;

    case 'details':
      if (!args[0]) {
        console.error('Usage: smithery details <qualified-name>');
        process.exit(1);
      }
      const details = await smithery.getServerDetails(args[0]);
      if (details) {
        console.log(JSON.stringify(details, null, 2));
      }
      break;

    default:
      console.log(`
🔧 Smithery Claude Integration

Usage:
  smithery search <query>              Search for MCP servers
  smithery install <qualified-name>    Install an MCP server
  smithery remove <server-name>        Remove an MCP server
  smithery list                        List installed servers
  smithery recommended                 Show recommended servers
  smithery details <qualified-name>    Get server details

Examples:
  smithery search "youtube transcript"
  smithery install @sinco-lab/mcp-youtube-transcript
  smithery remove youtube-transcript
      `);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = SmitheryIntegration;