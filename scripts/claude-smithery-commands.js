#!/usr/bin/env node

const SmitheryIntegration = require('./smithery-integration');

class ClaudeSmitheryCommands {
  constructor() {
    this.smithery = new SmitheryIntegration();
    this.suggestions = new Map();
    this.initializeSuggestions();
  }

  initializeSuggestions() {
    // Common task patterns and their recommended MCP servers
    this.suggestions.set(/youtube.*transcript/i, [
      '@sinco-lab/mcp-youtube-transcript',
      '@jikime/py-mcp-youtube-toolbox'
    ]);
    
    this.suggestions.set(/web.*scrap/i, [
      '@modelcontextprotocol/server-puppeteer',
      'firecrawl-mcp'
    ]);
    
    this.suggestions.set(/database|sql/i, [
      '@modelcontextprotocol/server-sqlite',
      '@upstash/context7-mcp'
    ]);
    
    this.suggestions.set(/memory|remember/i, [
      '@modelcontextprotocol/server-memory'
    ]);
    
    this.suggestions.set(/file.*operations?|read.*file|write.*file/i, [
      '@modelcontextprotocol/server-filesystem'
    ]);
    
    this.suggestions.set(/search|find/i, [
      'exa',
      '@upstash/context7-mcp'
    ]);
  }

  async analyzeTaskForMCPs(taskDescription) {
    const recommendations = [];
    
    for (const [pattern, servers] of this.suggestions) {
      if (pattern.test(taskDescription)) {
        recommendations.push(...servers);
      }
    }
    
    if (recommendations.length > 0) {
      console.log('🤖 Task Analysis: Recommended MCP servers for this task:');
      recommendations.forEach(server => {
        console.log(`   📦 ${server}`);
      });
      console.log('\n💡 Use /mcp-install <server-name> to install missing servers');
      return recommendations;
    }
    
    return [];
  }

  async smartInstall(taskDescription) {
    const recommendations = await this.analyzeTaskForMCPs(taskDescription);
    const config = this.smithery.loadConfig();
    const installed = Object.keys(config.mcpServers || {});
    
    const missing = recommendations.filter(server => {
      const serverName = server.replace('@', '').replace('/', '-');
      return !installed.includes(serverName) && !installed.includes(server);
    });
    
    if (missing.length === 0) {
      console.log('✅ All recommended MCP servers are already installed');
      return;
    }
    
    console.log(`🔧 Installing ${missing.length} recommended MCP servers...`);
    
    for (const server of missing) {
      console.log(`📦 Installing ${server}...`);
      await this.smithery.installServer(server);
    }
    
    console.log('✅ Smart installation complete!');
  }

  async quickSearch(query) {
    const servers = await this.smithery.searchServers(query);
    
    if (servers.length === 0) {
      console.log(`❌ No servers found for: ${query}`);
      return;
    }
    
    console.log(`🔍 Quick search results for "${query}":\n`);
    
    // Show top 5 results with install commands
    servers.slice(0, 5).forEach((server, index) => {
      console.log(`${index + 1}. 📦 ${server.qualifiedName}`);
      console.log(`   ${server.description.slice(0, 100)}...`);
      console.log(`   💾 Uses: ${server.useCount} | Remote: ${server.remote ? '☁️' : '💻'}`);
      console.log(`   🔧 Install: /mcp-install ${server.qualifiedName}`);
      console.log('');
    });
  }

  async autoComplete(partialCommand) {
    const commands = [
      '/smithery search',
      '/smithery install', 
      '/smithery remove',
      '/smithery list',
      '/smithery recommended',
      '/mcp-search',
      '/mcp-install',
      '/mcp-list'
    ];
    
    const matches = commands.filter(cmd => cmd.startsWith(partialCommand));
    
    if (matches.length > 0) {
      console.log('📝 Available commands:');
      matches.forEach(cmd => console.log(`   ${cmd}`));
    }
  }

  async handleSlashCommand(command, args) {
    switch (command) {
      case '/smithery':
        if (!args[0]) {
          await this.smithery.listRecommended();
          return;
        }
        process.argv = ['node', 'smithery-integration.js', ...args];
        await require('./smithery-integration');
        break;

      case '/mcp-search':
        if (!args[0]) {
          console.error('Usage: /mcp-search <query>');
          return;
        }
        await this.quickSearch(args.join(' '));
        break;

      case '/mcp-install':
        if (!args[0]) {
          console.error('Usage: /mcp-install <qualified-name> [server-name]');
          return;
        }
        await this.smithery.installServer(args[0], args[1]);
        break;

      case '/mcp-list':
        this.smithery.listInstalledServers();
        break;

      case '/mcp-smart-install':
        if (!args[0]) {
          console.error('Usage: /mcp-smart-install <task-description>');
          return;
        }
        await this.smartInstall(args.join(' '));
        break;

      case '/mcp-analyze':
        if (!args[0]) {
          console.error('Usage: /mcp-analyze <task-description>');
          return;
        }
        await this.analyzeTaskForMCPs(args.join(' '));
        break;

      default:
        console.log(`❌ Unknown command: ${command}`);
        this.showHelp();
    }
  }

  showHelp() {
    console.log(`
🔧 Claude Smithery Integration Commands

Basic Commands:
  /smithery                          Show recommended servers
  /mcp-search <query>               Quick search for MCP servers
  /mcp-install <qualified-name>     Install an MCP server
  /mcp-list                         List installed servers

Smart Commands:
  /mcp-smart-install <task>         Auto-install servers for a task
  /mcp-analyze <task>               Analyze task for MCP requirements

Examples:
  /mcp-search "youtube transcript"
  /mcp-install @sinco-lab/mcp-youtube-transcript
  /mcp-smart-install "I need to scrape YouTube transcripts"
  /mcp-analyze "fetch data from websites and databases"
    `);
  }
}

// CLI Interface
async function main() {
  const commands = new ClaudeSmitheryCommands();
  const command = process.argv[2];
  const args = process.argv.slice(3);

  if (!command) {
    commands.showHelp();
    return;
  }

  await commands.handleSlashCommand(command, args);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = ClaudeSmitheryCommands;