# YouTube Transcript MCP Setup Guide

## 🎯 Best MCP Server Options for YouTube Transcripts

Based on research, here are the top YouTube transcript MCP servers:

### 1. **@kimtaeyoon83/mcp-server-youtube-transcript** (Recommended)
- **Most popular** and well-maintained
- **Simple API** with multi-language support
- **Easy installation** via npx

### 2. **sparfenyuk/mcp-youtube-transcript-downloader**
- Good alternative option
- Requires UV tool for installation
- Comprehensive features

### 3. **Apify YouTube Transcript Scrapers**
- Cloud-based solution
- Requires Apify account and API key
- Best for bulk operations

## 📦 Installation Guide

### Option 1: Install @kimtaeyoon83/mcp-server-youtube-transcript

1. **Add to MCP Configuration**:

Create or edit your MCP configuration file:

```json
{
  "mcpServers": {
    "youtube-transcript": {
      "command": "npx",
      "args": ["-y", "@kimtaeyoon83/mcp-server-youtube-transcript"],
      "env": {}
    }
  }
}
```

2. **Location**: 
   - macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - Windows: `%APPDATA%\Claude\claude_desktop_config.json`
   - Linux: `~/.config/claude/claude_desktop_config.json`

3. **Restart Claude Desktop** after adding the configuration

### Option 2: Manual Installation

```bash
# Install globally
npm install -g @kimtaeyoon83/mcp-server-youtube-transcript

# Or use with npx (no installation needed)
npx @kimtaeyoon83/mcp-server-youtube-transcript
```

## 🔑 API Keys

### YouTube Data API (if needed)

Some MCP servers may require YouTube Data API v3 key:

1. **Get API Key**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create new project or select existing
   - Enable YouTube Data API v3
   - Create credentials → API Key

2. **Add to MCP Config**:
```json
{
  "mcpServers": {
    "youtube-transcript": {
      "command": "npx",
      "args": ["-y", "@kimtaeyoon83/mcp-server-youtube-transcript"],
      "env": {
        "YOUTUBE_API_KEY": "your-api-key-here"
      }
    }
  }
}
```

## 🚀 Usage Examples

Once installed, you can use the YouTube transcript tools:

```javascript
// Get transcript
await server.callTool("get_transcript", {
  url: "https://www.youtube.com/watch?v=VIDEO_ID",
  lang: "en"  // Optional: specify language
});

// Get transcript with metadata
await server.callTool("get_transcript_with_metadata", {
  url: "https://www.youtube.com/watch?v=VIDEO_ID"
});
```

## 📋 Features

- **Multi-language support**: Get transcripts in different languages
- **Auto-generated captions**: Access YouTube's auto-generated subtitles
- **Timestamp information**: Get timing data with transcripts
- **Batch processing**: Process multiple videos (depends on server)

## 🔍 Finding Your API Keys

### Check Google Account:
1. Visit [Google Cloud Console](https://console.cloud.google.com/apis/credentials)
2. Look for existing YouTube Data API keys
3. Check project quotas and limits

### Check Email for API Keys:
Search your email for:
- "YouTube API"
- "Google API Console"
- "API key created"
- "Google Cloud Platform"

## 🛠️ Troubleshooting

### Common Issues:

1. **"API key required"**:
   - Some videos require authentication
   - Add YouTube Data API key to environment

2. **"Transcript not available"**:
   - Video may not have captions
   - Try different language codes
   - Check if video is private

3. **Rate limiting**:
   - YouTube API has quotas
   - Consider caching transcripts
   - Use batch operations wisely

## 📚 Alternative: No API Key Required

The `@kimtaeyoon83/mcp-server-youtube-transcript` server often works without API keys for public videos by using YouTube's public transcript endpoint.

## 🎬 Test Videos

Try these public videos to test:
- TED Talks: Well-captioned content
- Educational channels: Usually have good transcripts
- News channels: Professional captions

---

**Next Steps:**
1. Choose your preferred MCP server
2. Add to Claude Desktop configuration
3. Restart Claude
4. Test with a YouTube video URL

Would you like me to help you set this up or search for your API keys in your Google account?