#!/bin/bash

echo "🔧 Fixing YouTube MCP Server..."

# Kill any running YouTube MCP processes
echo "Stopping old processes..."
pkill -f "youtube" 2>/dev/null || true
pkill -f "mcp-server" 2>/dev/null || true

# Clear npm cache for YouTube servers
echo "Clearing npm cache..."
npm cache clean --force 2>/dev/null || true

# Remove old YouTube server from npx cache
rm -rf ~/.npm/_npx/*youtube* 2>/dev/null || true

echo "✅ Cleanup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Quit Claude Desktop completely (Cmd+Q)"
echo "2. Wait 5 seconds"
echo "3. Reopen <PERSON>op"
echo ""
echo "The YouTube MCP server should now load with the API key."
echo ""
echo "🧪 To test after restart:"
echo "Ask Claude: 'Search YouTube for Python tutorials'"