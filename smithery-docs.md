# Smithery Documentation

> Comprehensive documentation for Smithery - A platform for AI-native services using Model Context Protocol (MCP)

## Table of Contents

1. [Introduction](#introduction)
2. [Using MCP Servers](#using-mcp-servers)
3. [Building MCP Servers](#building-mcp-servers)
4. [Configuration Reference](#configuration-reference)
5. [Data Policy](#data-policy)

---

## Introduction

Smithery is a platform to help developers find and ship AI-native services designed to communicate with AI agents. All listed services follow the Model Context Protocols (MCP) specification. The mission is to accelerate agentic AI by making agentic services accessible.

Smithery provides:
- A centralized hub for discovering MCP servers
- Hosting and distribution for MCP servers
- Standardized interfaces for tool integration and configs

### Quick Start

Two main paths:
1. **Use MCP Servers**: Learn how to integrate Smithery MCP servers into apps and agents
2. **Build MCP Servers**: Create and deploy your own MCP servers to Smithery

**Tip**: Install the Context7 MCP server and use this prompt:
```
/context7 get docs for smithery/sdk
```

### Model Context Protocol

The Model Context Protocol (MCP) is an "open protocol that enables seamless integration between LLMs and external data sources and tools." It provides a universal standard for connecting AI systems with context, eliminating information silos.

**Key benefits:**
- Simplifies development of agentic applications
- Enables building intelligent IDEs, chat interfaces, and custom AI workflows
- Provides a standardized protocol instead of custom implementations
- Improves system maintainability and scalability

The documentation emphasizes MCP as a solution to fragmented AI tool integrations, making it easier for developers to create more sophisticated AI applications.

---

## Using MCP Servers

### Integration Approaches

At a high-level, there are two main ways to integrate Smithery with your client application:

#### 1. Direct User Authentication (Simpler)

With this simpler approach, users authenticate directly with Smithery:
- When needed, your application redirects users to create their own Smithery accounts
- Smithery handles storing auth secrets and configurations for the user
- Your client only needs the user's Smithery API key to establish connections

**Benefits:**
- Simpler implementation for client developers
- Reduced security burden on your application
- Users can reuse their configurations across multiple clients

#### 2. White-Label Integration

With this approach, your application authenticates with Smithery under your developer account and proxies user connections:
- Your application manages user secrets and configurations
- You authenticate with Smithery using your developer credentials
- You send user configurations with each connection request

**Benefits:**
- Fully white-labeled experience within your application
- Complete control over the user experience
- No need for users to create Smithery accounts

**Considerations:**
- More complex implementation
- Your application must securely store user secrets
- You must send configuration with each connection request

### Connecting to MCPs

#### Prerequisites

Before connecting to MCPs, you need:
1. Install required packages:
   ```bash
   npm install @modelcontextprotocol/sdk @smithery/sdk
   ```
2. A Smithery API key from the [Smithery Dashboard](https://smithery.ai/account/api-keys)
3. Node.js version 18 or higher

#### Connection Methods

##### Using a Profile (Recommended)

If you have a saved server configuration in a Smithery profile, you can connect directly using the profile ID:

```typescript
import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js"
import { Client } from "@modelcontextprotocol/sdk/client/index.js"

const profileId = "your-profile-id"
const apiKey = "your-api-key"
const serverName = "server-name"

const transport = new StreamableHTTPClientTransport(
  `https://server.smithery.ai/${serverName}/mcp?profile=${profileId}&api_key=${apiKey}`
)

const client = new Client({
  name: "Test Client",
  version: "1.0.0"
})

await client.connect(transport)
```

##### Manual Configuration

1. **Get Server Details**
   
   Retrieve server details from the Registry API:

   ```typescript
   const qualifiedName = '@browserbasehq/mcp-browserbase'
   const response = await fetch(
     `https://registry.smithery.ai/servers/${qualifiedName}`,
     {
       headers: {
         'Authorization': `Bearer ${apiKey}`,
         'Accept': 'application/json'
       }
     }
   );

   const serverInfo = await response.json();
   const httpConnection = serverInfo.connections.find(c => c.type === 'http');

   const config = {
     browserbaseApiKey: "your-browserbase-api-key",
     browserbaseProjectId: "your-project-id"
   }
   ```

### Registry API

The Smithery Registry API provides a programmatic way to search for and retrieve information about MCP servers.

#### Authentication
- Requires a bearer token
- API keys can be created at https://smithery.ai/account/api-keys

#### Endpoints

1. **List Servers**
   ```
   GET https://registry.smithery.ai/servers
   ```
   
   Supports query parameters:
   - `q`: Semantic search query
   - `page`: Pagination (default: 1)
   - `pageSize`: Results per page (default: 10)

2. **Get Specific Server**
   ```
   GET https://registry.smithery.ai/servers/{qualifiedName}
   ```

#### Filtering Options
- Text search
- Filter by owner: `owner:username`
- Filter by repository: `repo:repository-name`
- Deployment status: `is:deployed`
- Verification status: `is:verified`

#### Example Request
```javascript
const response = await fetch(
  'https://registry.smithery.ai/servers?q=owner:mem0ai is:verified memory',
  {
    headers: {
      'Authorization': 'Bearer your-api-token',
      'Accept': 'application/json'
    }
  }
);
```

### Configuration Profiles

Configuration Profiles are a way to group session configurations for AI agents.

#### Purpose
- Allow bundling of specific servers and their configurations
- Similar to desktop login profiles that manage application access

#### Example Use Case
A "Coding Assistant" profile could connect multiple servers:
- Gitingest MCP for code repository access
- Memory Tool for context storage
- Context7 for semantic search

#### Adding Servers to Profiles
Two methods:
- From any server page, select profile and click "Connect"
- From Account > Profiles, use "Add Server" search bar

#### Usage Methods
- **Individual Server Connection**: Use profile to automatically load saved configuration
- **Toolbox**: Load all configured tools from a profile in a single connection

#### Key Benefits
- Simplify managing multiple API keys and configurations
- Reuse configurations across different agents
- Optional configuration fields with profile as default fallback

*Note: Profiles are "currently under active development" and welcome community feedback.*

### Deep Links Integration

Deep links provide a seamless way to integrate Smithery MCP servers into supported clients.

#### URL Format
```
${clientScheme}://{optionalDeepLinkHandler}/mcp/install?name=${encodedDisplayName}&config=${encodedConfig}
```

#### Configuration Types

1. **StdioMCPConfig**:
   ```typescript
   interface StdioMCPConfig {
     type: "stdio";
     command: string;  // e.g., "npx"
     args: string[];   // Command line arguments
   }
   ```

2. **HttpMCPConfig**:
   ```typescript
   interface HttpMCPConfig {
     type: "http";
     url: string;  // URL of the MCP server
   }
   ```

#### Example Configurations

**Stdio-based Configuration:**
```json
{
  "type": "stdio",
  "command": "npx",
  "args": ["-y", "@smithery/cli@latest", "run", "@wonderwhy-er/desktop-commander"]
}
```

**HTTP-based Configuration:**
```json
{
  "type": "http",
  "url": "https://server.smithery.ai/exa/mcp"
}
```

#### Handling Deep Links

```typescript
function handleDeepLink(url: string) {
  const urlObj = new URL(url)
  const name = urlObj.searchParams.get('name')
  const config = JSON.parse(decodeURIComponent(
    urlObj.searchParams.get('config')
  ))
  return { config, name }
}
```

### Session Configuration

Session configurations are JSON objects that customize how an MCP server behaves for a specific client connection.

#### How Configurations Work
- Each MCP server can define configuration parameters
- Parameters can include API keys, model settings, temperature values
- Configurations are bound to individual sessions

#### Configuration Declaration
- Defined by MCP server authors using JSON Schema
- Configured in the `configSchema` field of `smithery.yaml`
- Supports standard JSON Schema features:
  - Data types
  - Required fields
  - Default values
  - Enumerated options
  - Min/max constraints

#### Example Configuration Schema
```yaml
startCommand:
  type: http
  configSchema:
    type: object
    required: ["openaiApiKey"]
    properties:
      openaiApiKey:
        type: string
        title: "OpenAI API Key"
      modelName:
        type: string
        default: "gpt-4"
        enum: ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"]
```

#### Connection Process
1. Retrieve configuration schema via Registry API
2. Send configuration using dot-notation query parameters

#### Best Practices
- Provide clear documentation
- Set sensible defaults
- Use enums for limited choices
- Handle configuration securely
- Keep configurations small

#### Key Limitations
- Configurations cannot be changed mid-session
- Sensitive information should be saved as profiles
- Reserved parameters like `api_key` are not passed to servers

---

## Building MCP Servers

Learn how to build your Model Context Protocol (MCP) server on Smithery.

Smithery supports developers building MCPs by providing CI/CD deployments and hosting.

### Benefits of Hosting

- Smithery will show a tool playground on your server page, allowing users to discover and try your MCP online
- Users can call your server without installing dependencies or being concerned about security
- Smithery will rank hosted servers higher in search results

For developers, Smithery provides an [SDK](https://github.com/smithery-ai/sdk/) to make it easier to deploy servers on Smithery.

### Getting Started with TypeScript

#### Prerequisites
- Node.js 18+
- npm or yarn
- Smithery API key

#### Quick Start Process

1. **Install CLI**
   ```bash
   npm install -g @smithery/cli
   ```

2. **Initialize Project**
   ```bash
   npm create smithery
   ```

3. **Create Server in `src/index.ts`**
   ```typescript
   import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
   import { z } from 'zod';

   export default function ({ config }) {
     const server = new McpServer({
       name: 'My MCP Server',
       version: '1.0.0'
     });

     server.tool(
       'hello',
       'Say hello to someone',
       { name: z.string().describe('Name to greet') },
       async ({ name }) => {
         return {
           content: [{ type: 'text', text: `Hello, ${name}!` }]
         };
       }
     );

     return server.server;
   }
   ```

4. **Configure `package.json` entry point**

5. **Start Development**
   ```bash
   npx @smithery/cli dev
   ```

6. **Deploy with `smithery.yaml`**

#### Advanced Configuration Options
- Optional configuration schema using Zod
- Custom build configurations in `smithery.config.js`
- Ability to mark external dependencies

### Deployments

Smithery allows hosting MCP servers over Streamable HTTP, with two primary deployment methods:

#### 1. TypeScript Deploy
- Fastest method for TypeScript MCP servers
- Requires:
  - TypeScript MCP server
  - Smithery CLI
  - Repository with `package.json`

**Setup:**
Create a `smithery.yaml` file with:
```yaml
runtime: "typescript"
```

**Deployment steps:**
1. Push code to GitHub
2. Connect GitHub to Smithery
3. Navigate to Deployments tab
4. Click Deploy

#### 2. Custom Deploy
- For advanced users with Docker containers
- Supports any programming language

**Technical Requirements:**
- Docker container implementing Streamable HTTP
- Dockerfile in repository
- Server listening on `PORT` environment variable

**Key HTTP Endpoint Requirements:**
- `/mcp` endpoint
- Handle GET, POST, DELETE requests
- Listen on `PORT` environment variable

**Configuration Handling:**
- Passed via query parameters with dot notation
- Example: `GET/POST /mcp?server.host=localhost&server.port=8080&apiKey=secret123`

**Best Practice: Tool Discovery**
- List tools without authentication
- Validate API keys only when tools are invoked
- Allow users to discover server capabilities before configuration

### Git Integration

Smithery for GitHub automatically verifies and deploys MCP servers directly from repositories.

#### Repository Permissions

Smithery requires several GitHub permissions:

| Permission | Read | Write | Description |
|------------|------|-------|-------------|
| **Contents** | ✓ | ✓ | Fetch and write source code for generating pull requests on new branches |
| **Pull Requests** | ✓ | ✓ | Create pull requests and add interactive server inspection links |
| **Metadata** | ✓ | ✗ | Access basic repository information |
| **Checks** | ✓ | ✓ | Create and view detailed server status reports |
| **Commit Status** | ✓ | ✓ | Mark commits with deployment status |
| **Deployments** | ✓ | ✓ | Create and manage MCP server deployment processes |
| **Issues** | ✓ | ✓ | Automatically create issues for detected problems |
| **Repository Hooks** | ✓ | ✓ | Manage webhooks for code change notifications |

#### Installing the GitHub App

**Automatic Installation:**
When creating a new MCP server, users are automatically prompted to install the GitHub app.

**Manual Installation:**
1. Visit https://github.com/apps/smithery-ai
2. Click "Install"
3. Choose repository access:
   - All repositories
   - Selected repositories only
4. Complete installation by reviewing permissions

**Managing Repository Access:**
After installation, users can modify repository access:
- Go to GitHub account settings
- Navigate to **Applications** → **Installed GitHub Apps**
- Find **Smithery** and click **Configure**
- Add or remove repository access

---

## Configuration Reference

### Project Configuration

This guide explains how to configure your project for deployment on Smithery. The project configuration tells Smithery how to deploy your server and should be located in the base of your repository.

#### Configuration Files

There are two required configuration files:

1. **Dockerfile**: Defines how to build your server's container image
2. **smithery.yaml**: Specifies how to start and run your server

#### Automatic Setup

Smithery will attempt to automatically generate a pull-request with these files when you trigger a deployment. However, setup might fail, requiring manual configuration.

### smithery.yaml Reference

The `smithery.yaml` file is a configuration file for MCP servers on Smithery, which must be placed in the repository root.

#### Runtime Configuration

The `runtime` setting specifies the deployment environment:

- `"typescript"`: Uses Smithery CLI to build TypeScript projects directly
- `"container"`: Uses Docker containers for deployment (supports any language)

Example:
```yaml
runtime: "typescript"  # or "container"
```

#### TypeScript Runtime

When using `runtime: "typescript"`, Smithery uses the Smithery CLI to build and deploy the server directly.

Key properties:
- `runtime`: Must be set to `"typescript"`
- `env`: Optional environment variables

#### Container Runtime

For `runtime: "container"`, Smithery uses Docker containers with more configuration options:

**startCommand** (Required for container runtime):
- `type`: Must be `"http"`
- `configSchema`: JSON Schema defining configuration options
- `exampleConfig`: Sample configuration values

**build** (Optional Docker build configuration):
- `dockerfile`: Path to Dockerfile (default: "Dockerfile")
- `dockerBuildPath`: Build context path (default: ".")

**env** (Environment variables):
```yaml
env:
  NODE_ENV: "production"
  DEBUG: "true"
```

### Dockerfile Configuration

Create a `Dockerfile` in your repository root that defines how to build your MCP server. Your Dockerfile should be created such that running your Docker image will start your Streamable HTTP server.

#### Example Dockerfile

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy application code
COPY . .

# Build the application
RUN npm run build

CMD ["node", "dist/index.js"]
```

#### Requirements

**We only support Linux Docker images on major distros (Alpine/Debian-based) and expect `sh` to run in your container. Other distros are untested and may not deploy.**

#### Examples

You can find examples of Dockerfiles in Smithery's [reference implementations](https://github.com/smithery-ai/mcp-servers).

### Project Structure

#### Subdirectories

For monorepos, place your Dockerfile and smithery.yaml in the subdirectory containing your package. You'll need to specify the base directory in your server settings.

**Example:**
1. Place Dockerfile and smithery.yaml in `packages/mcp-server`
2. Set base directory to `packages/mcp-server` in server settings

#### Best Practices

1. **Testing**: Test your MCP server locally using MCP Inspector
2. **Configuration**: Use `configSchema` to define and validate configuration options
3. **Docker Optimization**: Keep Docker image size minimal

---

## Data Policy

This document outlines how Smithery handles data when using Model Context Protocol (MCP) servers. Smithery supports two types of MCPs: hosted and local.

### Hosted MCPs

Hosted MCPs are deployed by developers on Smithery's servers, identifiable by a green dot on the website.

1. Smithery tracks calls to hosted MCPs for analytics to improve service and display usage counters
2. Configuration data for hosted MCPs is ephemeral and not stored on servers

**"In all cases, we do not sell your data to third parties."**

**Note about MCP Developer Code**: While Smithery provides infrastructure, individual MCP developers control their code's behavior. Users should review each MCP's documentation for specific data handling practices.

Enterprise users requiring zero retention can contact Smithery.

### Local MCPs

Local MCPs are pulled from Smithery and run on the user's local machine via CLI.

Smithery performs two tracking types for local MCPs:
1. **Install tracking**: Anonymous installation count tracking
2. **Tool call tracking**: With user consent, tracks tool and MCP names (content redacted)

Configuration arguments are ephemeral and not stored on servers.

### Playground

Playground conversations may be tracked for analytics, with an option to opt out in Playground Settings.

Smithery continuously works to improve data practices. Users with questions can contact the team via email or Discord.

---

## Resources

- [Smithery SDK](https://github.com/smithery-ai/sdk/)
- [Reference Implementations](https://github.com/smithery-ai/mcp-servers)
- [Smithery Dashboard](https://smithery.ai/account/api-keys)
- [GitHub App Installation](https://github.com/apps/smithery-ai)

---

*This documentation was compiled from the official Smithery documentation pages on July 9, 2025.*